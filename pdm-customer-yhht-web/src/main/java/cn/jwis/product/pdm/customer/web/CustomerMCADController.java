package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.date.DateUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.product.pdm.cad.mcad.dto.MCADCreateDTO;
import cn.jwis.product.pdm.cad.mcad.entity.CADFile;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.relation.Primary;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.MCADService;
import cn.jwis.product.pdm.customer.entity.CADFileDownloadDTO;
import cn.jwis.product.pdm.customer.entity.MCADFileInfoDTO;
import cn.jwis.product.pdm.customer.entity.PartMCADFilesDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomerFIleServiceImpl;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ych
 * @Date ： 2024/10/23
 * @Description :mcad创建Controller
 */

@RestController
@RequestMapping({"/mcad"})
@Api(tags = {"mcad"},value = "mcad", description = "mcad")
public class CustomerMCADController {

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    MCADService mcadService;
    @Autowired
    private InstanceHelper instanceHelper;

    @Autowired
    FileService fileService;

    @Autowired
    private JWICommonService jwiCommonService;

    @Autowired
    private CollectionRuleHelper collectionRuleHelper;

    @Autowired
    private MCADHelper mcadHelper;

    @Autowired
    private CustomerFIleServiceImpl customerFileService;


    @PostMapping({"/create"})
    @ApiOperation(
            response = Result.class,
            value = "customer创建MCAD-重写",
            notes = "customer创建MCAD-重写"
    )
    public Result create(@RequestBody MCADCreateDTO dto) throws JWIException {
        MCADIteration mcadIteration = new MCADIteration();
        BeanUtils.copyProperties(dto, mcadIteration);
        MCADIteration byName = this.mcadService.findByName(dto.getName());
        if (null != byName) {
            return Result.Fail("已存在相同名称MCAD对象 编码:" + byName.getNumber() + " 名称:" + byName.getName());
        }
        this.mcadService.setLocation(mcadIteration, dto.getLocationInfo());
        MCADIteration result = commonAbilityHelper.doCreate(mcadIteration);
        result.setLifecycleStatus("Released");
        result = commonAbilityHelper.doUpdate(result);

        List<File> primaryFile = dto.getPrimaryFile();
        if (null != primaryFile && primaryFile.size() > 0) {
            File file = primaryFile.get(0);
            FileMetadata fileMetadata = fileService.findByOid(file.getOid());
            Assert.notNull(fileMetadata, "文件不存在");
            CADFile cadFile = new CADFile();
            cadFile.setOid(OidGenerator.newOid());
            cadFile.setType(CADFile.TYPE);
            cadFile.setFileType("Creo 7.0");
            cadFile.setPrimary(Boolean.TRUE);
            cadFile.setLastModified(DateUtil.now());
            cadFile.setFileName(fileMetadata.getFileOriginalName());
            cadFile.setUrl(fileMetadata.getOid());
            CADFile cadFile1 = mcadService.createCADFile(cadFile);
            mcadService.linkMCADFile(mcadIteration.getOid(), cadFile1.getOid(), Primary.TYPE);
        }
        return Result.success(result);
    }

    /**
     * 根据物料号查询MCAD对象文件
     *
     * @param partNumbers 物料号列表
     * @return 包含MCAD文件下载链接的结果
     */
    @IgnoreRestUrlAccess
    @PostMapping("/queryMCADFilesByPartNumbers")
    @ApiOperation(
            response = Result.class,
            value = "根据物料号查询MCAD对象文件",
            notes = "根据一组物料号，查询物料号关联的MCAD对象的STP格式文件和CADDrawing的附件"
    )
    public Result<Map<String, PartMCADFilesDTO>> queryMCADFilesByPartNumbers(
            @ApiParam(value = "物料号列表", required = true)
            @RequestBody List<String> partNumbers) {

        try {
            Map<String, PartMCADFilesDTO> result = new HashMap<>();

            for (String partNumber : partNumbers) {
                PartMCADFilesDTO partFiles = new PartMCADFilesDTO();
                partFiles.setPartNumber(partNumber);
                partFiles.setMcadFiles(new ArrayList<>());

                // 4.1 根据料号查询对应的相关对象（相关MCAD图档）
                List<InstanceEntity> relatedMCADs = queryRelatedMCADsByPartNumber(partNumber);

                for (InstanceEntity mcadInstance : relatedMCADs) {
                    MCADFileInfoDTO mcadFileInfo = new MCADFileInfoDTO();
                    mcadFileInfo.setMcadOid(mcadInstance.getOid());
                    mcadFileInfo.setMcadName(mcadInstance.getName());
                    mcadFileInfo.setMcadType(mcadInstance.getModelDefinition());
                    mcadFileInfo.setFiles(new ArrayList<>());

                    // 4.2 根据MCAD的类型分别去查询对应的文件
                    List<CADFileDownloadDTO> files = collectMCADFiles(mcadInstance);
                    mcadFileInfo.setFiles(files);

                    partFiles.getMcadFiles().add(mcadFileInfo);
                }

                result.put(partNumber, partFiles);
            }

            return Result.success(result);

        } catch (Exception e) {
            return Result.Fail("查询MCAD文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据料号查询相关的MCAD对象
     */
    private List<InstanceEntity> queryRelatedMCADsByPartNumber(String partNumber) {
        // 首先根据料号查找Part对象
        /*List<InstanceEntity> parts = jwiCommonService.dynamicQuery(
                Part.TYPE,
                cn.jwis.framework.database.core.query.dynamic.Condition.where("number").eq(partNumber),
                InstanceEntity.class
        );*/

        ArrayList<String> slaveNumbers = new ArrayList<>();
        slaveNumbers.add(partNumber);
        List<PartIteration> parts = commonAbilityHelper.findDetailEntityByNumber(slaveNumbers, PartIteration.TYPE)
                .stream().map(item -> (PartIteration) item).collect(Collectors.toList());

        List<InstanceEntity> allRelatedMCADs = new ArrayList<>();
        // 查询Part关联的MCAD图档
        for (PartIteration part : parts) {
            List<InstanceEntity> relatedMCADs = queryWithCollectionRule(
                    part.getOid(),
                    "Part_Related_Object",
                    "相关MCAD图档",
                    Part.TYPE,
                    RelationConstraint.II
            );
            allRelatedMCADs.addAll(relatedMCADs);
        }


        return allRelatedMCADs;
    }

    /**
     * 查询集合规则相关对象
     */
/*    private List<InstanceEntity> queryWithCollectionRule(String oid, String appliedType, String ruleName,
                                                         String mainObjectType, RelationConstraint relationConstraint) {
        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType(appliedType, mainObjectType)
                .stream().filter(it -> ruleName.equals(it.getRelationDisplayName())).findFirst().orElse(null);

        return Optional.ofNullable(collectionRule).map(rule -> {
            return instanceHelper.findRelatedInstance(oid, rule.getRelationType(), rule.getToType(), relationConstraint);
        }).orElse(new ArrayList<>());
    }*/
    private List<InstanceEntity> queryWithCollectionRule(String oid,String appliedType,String ruleName,
                                                         String mainObjectType,RelationConstraint relationConstraint) {
        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType(appliedType, mainObjectType)
                .stream().filter(it -> ruleName.equals(it.getRelationDisplayName())).findFirst().orElse(null);

        return Optional.ofNullable(collectionRule).map(rule -> {
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint(relationConstraint);
            BeanUtils.copyProperties(collectionRule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(oid);
            return Optional.ofNullable(instanceHelper.fuzzyRelated(relatedFuzzyDTO)).orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());
    }

    /**
     * 收集MCAD文件
     */
    private List<CADFileDownloadDTO> collectMCADFiles(InstanceEntity mcadInstance) {
        List<CADFileDownloadDTO> result = new ArrayList<>();

        try {
            String modelDefinition = mcadInstance.getModelDefinition();
            List<CADFile> cadFiles = new ArrayList<>();

            switch (modelDefinition) {
                case "CADPart":
                case "CADAssembly":
                    // 4.2.1 零件和装配：优先查找STP格式文件，如果没有则查找源文件
                    cadFiles = mcadHelper.findCADFileSTPByOid(mcadInstance.getOid(), "stp");
                    if (cadFiles.isEmpty()) {
                        cadFiles = mcadHelper.findCADFileSTPByOid(mcadInstance.getOid(), "primary");
                    }
                    break;

                case "CADDrawing":
                    // 4.2.2 工程图：查找附件（已签名的PDF）
                    cadFiles = mcadHelper.findCADFile(mcadInstance.getOid())
                            .stream()
                            .filter(cadFile -> !cadFile.isPrimary() && "thumbnail".equals(cadFile.getFileType()))
                            .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
                    break;

                default:
                    break;
            }

            // 4.3 生成可下载的minio链接
            for (CADFile cadFile : cadFiles) {
                CADFileDownloadDTO fileDto = new CADFileDownloadDTO();
                fileDto.setFileName(cadFile.getFileName());
                fileDto.setFileType(cadFile.getFileType());
                fileDto.setFileOid(cadFile.getUrl());
                fileDto.setPrimary(cadFile.isPrimary());

                try {
                    // 使用CustomerFileServiceImpl的getUrlByOidForPreview来获取下载链接
                    String downloadUrl = customerFileService.getUrlByOidForPreview(
                            cadFile.getUrl(),
                            mcadInstance.getType(),
                            mcadInstance.getOid()
                    );
                    fileDto.setDownloadUrl(downloadUrl);
                } catch (Exception e) {
                    fileDto.setDownloadUrl("");
                    fileDto.setErrorMessage("生成下载链接失败: " + e.getMessage());
                }

                result.add(fileDto);
            }

        } catch (Exception e) {
            CADFileDownloadDTO errorDto = new CADFileDownloadDTO();
            errorDto.setFileName("ERROR");
            errorDto.setErrorMessage("收集文件失败: " + e.getMessage());
            result.add(errorDto);
        }

        return result;
    }




}
